export const BASE_URL_STAG = "http://localhost:8080/api/";
export const BASE_URL_DEV = "https://dev.api.docmobilapp.com/api/";
export const BASE_URL_PROD = "https://api.docmobilapp.com/api/";

export const API_URLS = {
  // AUTH
  me_api_url: "auth/me",
  login_doctor_api_url: "auth/loginDoctor",
  login_patient_api_url: "auth/loginPatient",
  // DOCTOR
  create_doctor_api_url: "doctor/signup",
  forgot_doctorPassword_api_url: "doctor/requestForgotPassword",
  reset_doctorPassword_api_url: "doctor/resetPassword",
  verify_forgot_doctor_passowrd_otp_api_url: "doctor/verifyForgotPasswordOtp",
  update_doctor_api_url: "doctor/update",
  fetch_doctors_api_url: "doctor/fetchDoctors",
  fetch_doctor_with_id_api_url: "doctor/fetchDoctorById",

  // PATIENT
  create_patient_api_url: "patient/signup",
  forgot_patientPassword_api_url: "patient/requestForgotPassword",
  reset_patientPassword_api_url: "patient/resetPassword",
  verify_forgot_patient_passowrd_otp_api_url: "patient/verifyForgotPasswordOtp",
  update_patient_api_url: "patient/update",
  fetch_concerned_patients_api_url: "patient/fetchConcernedPatients",
  fetch_patient_by_id_api_url: "patient/fetchPatientById",

  // CONSULTATIONS
  create_multiple_consultations_api_url: "consultation/createMultiple",
  update_consultations_api_url: "consultation/updateConsultationsDoctor",
  get_consultations_api_url: "consultation/getDoctorConsultations",
  // SCHEDULE
  get_weekly_schedule_doctor_api_url: "schedule/getWeeklyScheduleOfDoctor",
  get_override_schedule_doctor_api_url: "schedule/getOverrideScheduleOfDoctor",
  create_weekly_schedule_doctor_api_url: "schedule/createWeeklyMultipleDays",
  create_override_schedule_doctor_api_url:
    "schedule/createOverrideMultipleDates",
  get_slots_details_doctor_api_url: "schedule/getDoctorSlotDetails",

  // APPOINTMENTS
  book_patient_appointment_api_url: "appointment/book",
  get_appointment_by_id_api_url: "appointment/getById",
  get_appointments_by_patientId_api_url: "appointment/getByPatientId",
  get_appointments_by_doctorId_api_url: "appointment/getByDoctorId",

  // PRE-APPOINTMENT FORM
  create_preAppointmentForm_api_url: "preAppointmentForm/create",
  update_preAppointmentForm_api_url: "preAppointmentForm/update",
  get_preAppointmentForm_by_id_api_url: "preAppointmentForm/getById",
  get_preAppointmentForm_by_appointmentId_api_url:
    "preAppointmentForm/getByAppointmentId",
  get_preAppointmentForm_by_patientId_api_url:
    "preAppointmentForm/getByPatientId",

  // SOAP FORM
  create_soapForm_api_url: "soapForm/create",
  update_soapForm_api_url: "soapForm/update",
  get_soapForm_by_id_api_url: "soapForm/getById",
  get_soapForm_by_appointmentId_api_url: "soapForm/getByAppointmentId",

  // ROS FORM (Review of Systems)
  create_rosForm_api_url: "rosForm/create",
  update_rosForm_api_url: "rosForm/update",
  get_rosForm_by_id_api_url: "rosForm/getById",
  get_rosForm_by_appointmentId_api_url: "rosForm/getByAppointmentId",

  // LAB FORM
  create_labForm_api_url: "labForm/create",
  update_labForm_api_url: "labForm/update",
  get_labForm_by_id_api_url: "labForm/getById",
  get_labForm_by_appointmentId_api_url: "labForm/getByAppointmentId",
  get_labForm_by_patientId_api_url: "labForm/getByPatientId",

  // IMAGING FORM
  create_imagingForm_api_url: "imagingForm/create",
  update_imagingForm_api_url: "imagingForm/update",
  get_imagingForm_by_id_api_url: "imagingForm/getById",
  get_imagingForm_by_appointmentId_api_url: "imagingForm/getByAppointmentId",

  // MEDICAL CERTIFICATE
  create_medicalCertificate_api_url: "medicalCertificate/create",
  update_medicalCertificate_api_url: "medicalCertificate/update",
  get_medicalCertificate_by_id_api_url: "medicalCertificate/getById",
  get_medicalCertificate_by_appointmentId_api_url:
    "medicalCertificate/getByAppointmentId",
  get_medicalCertificate_by_patientId_api_url:
    "medicalCertificate/getByPatientId",

  // PRESCRIPTION FORM
  create_prescriptionForm_api_url: "prescriptionForm/create",
  update_prescriptionForm_api_url: "prescriptionForm/update",
  delete_prescriptionForm_api_url: "prescriptionForm/delete",
  get_prescriptionForm_by_id_api_url: "prescriptionForm/getById",
  get_prescriptionForm_by_appointmentId_api_url:
    "prescriptionForm/getByAppointmentId",
  get_prescriptionForm_by_patientId_api_url: "prescriptionForm/getByPatientId",
  // COMPLIANCE
  create_compliance_api_url: "compliance/create",
  update_compliance_api_url: "compliance/update",
  delete_compliance_api_url: "compliance/delete",
  get_compliance_by_id_api_url: "compliance/getById",
  get_compliances_by_appointmentId_api_url: "compliance/getByAppointmentId",
  get_doctor_compliances_by_appointmentId_api_url:
    "compliance/getDoctorCompliancesByAppointmentId",
  get_patient_compliances_by_appointmentId_api_url:
    "compliance/getPatientCompliancesByAppointmentId",

  // EMR
  get_concerned_doctors_api_url: "emr/getConcernedDoctors",
  share_emr_api_url: "emr/share",
  get_shared_emrs_api_url: "emr/getSharedEmrs",
};
